"""
Module management models for base addon
"""
import os

# Import base model - use sync version for compatibility
from erp.models.base import BaseModel
from erp.fields import Char, Text, Boolean, Selection, Datetime, Integer

# Ensure this addon is available via standardized import
from erp.addons import ensure_addon_import
addon_path = os.path.join(os.path.dirname(__file__), '..')
ensure_addon_import('base', addon_path)


class IrModuleModule(BaseModel):
    """Model for managing addon modules"""

    _name = 'ir.module.module'
    _description = 'Module'
    _table = 'ir_module_module'
    _auto_create_table = False  # Prevent automatic table creation

    # Override name field to be the technical name
    name = Char(string='Technical Name', required=True, unique=True, help='Technical name of the module')
    display_name = Char(string='Display Name', required=True, help='Human readable name')
    summary = Char(string='Summary', help='Short description of the module')
    description = Text(string='Description', help='Detailed description of the module')
    author = Char(string='Author', help='Module author')
    version = Char(string='Version', help='Module version')
    category = Char(string='Category', help='Module category')
    website = Char(string='Website', help='Module website')

    state = Selection([
        ('uninstalled', 'Not Installed'),
        ('installed', 'Installed'),
        ('to_upgrade', 'To be upgraded'),
        ('to_remove', 'To be removed'),
        ('to_install', 'To be installed'),
    ], string='Status', default='uninstalled', required=True, index=True)

    installable = Boolean(string='Installable', default=True,
                         help='Whether this module is installable or not')
    auto_install = Boolean(string='Automatic Installation', default=False,
                          help='An auto-installable module is automatically installed when all its dependencies are satisfied')
    application = Boolean(string='Application', default=False,
                         help='Whether this module is an application or not')
    sequence = Integer(string='Sequence', default=100,
                      help='Sequence for ordering modules')

    actionAt = Datetime(string='Last Action', readonly=True,
                       help='Timestamp of the last action performed on this module')

    def button_install(self):
        """Install the module"""
        from datetime import datetime
        self.write({'state': 'to_install', 'actionAt': datetime.now()})

    def button_uninstall(self):
        """Uninstall the module"""
        from datetime import datetime
        self.write({'state': 'to_remove', 'actionAt': datetime.now()})

    def button_upgrade(self):
        """Upgrade the module"""
        from datetime import datetime
        self.write({'state': 'to_upgrade', 'actionAt': datetime.now()})

    def button_immediate_install(self):
        """Install the module immediately"""
        from datetime import datetime
        self.write({'state': 'installed', 'actionAt': datetime.now()})

    def button_immediate_uninstall(self):
        """Uninstall the module immediately"""
        from datetime import datetime
        self.write({'state': 'uninstalled', 'actionAt': datetime.now()})

    @classmethod
    def get_installed_modules(cls):
        """Get list of installed modules"""
        return cls.search([('state', '=', 'installed')])

    @classmethod
    def get_installable_modules(cls):
        """Get list of installable modules"""
        return cls.search([('installable', '=', True)])

    def install_dependencies(self):
        """Install module dependencies (placeholder)"""
        # In a real implementation, this would parse dependencies
        # and install them recursively
        pass

# The model is automatically registered via the metaclass
